import { styled } from "../utils/styled";
import { View, Text, Pressable, Modal, ScrollView } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(ScrollView)`
  flex: 1;
  padding: 16px;
`;

export const Section = styled(View)`
  margin-bottom: 24px;
`;

export const SectionTitle = styled(Text)`
  font-size: 18px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const SettingItem = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 12px;
  margin-bottom: 8px;
`;

export const SettingText = styled(Text)`
  flex: 1;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-left: 12px;
`;

export const CurrentLanguage = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-right: 8px;
`;

export const ModalOverlay = styled(Modal)``;

export const ModalContent = styled(View)`
  flex: 1;
  justify-content: flex-end;
  background-color: rgba(0, 0, 0, 0.5);
`;

export const ModalInnerContent = styled(View)`
  background-color: ${({ theme }) => theme.colors.card};
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 24px;
  padding-bottom: 32px;
  max-height: 60%;
  min-height: 200px;
`;

export const ModalTitle = styled(Text)`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
  text-align: center;
`;

export const LanguageOption = styled(Pressable)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: transparent;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.divider};
`;

export const LanguageText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const SelectedLanguageText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
`;

export const CloseButton = styled(Pressable)`
  margin-top: 16px;
  margin-horizontal: 0px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 12px;
  align-items: center;
`;

export const CloseButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.white};
`;

export const LogoutModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
`;

export const LogoutModalContent = styled.View`
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 16px;
  padding: 24px;
  width: 85%;
  align-items: center;
`;

export const LogoutModalTitle = styled.Text`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
  text-align: center;
`;

export const LogoutModalMessage = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: 24px;
  text-align: center;
  line-height: 22px;
`;

export const LogoutButtonContainer = styled.View`
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
  gap: 12px;
`;

export const LogoutButton = styled(Pressable)<{
  variant: "cancel" | "confirm";
}>`
  flex: 1;
  padding: 14px;
  border-radius: 8px;
  align-items: center;
  background-color: ${({ theme, variant }) =>
    variant === "confirm" ? theme.colors.error : theme.colors.cardSecondary};
`;

export const LogoutButtonText = styled.Text<{ variant: "cancel" | "confirm" }>`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme, variant }) =>
    variant === "confirm" ? theme.colors.white : theme.colors.text};
`;
